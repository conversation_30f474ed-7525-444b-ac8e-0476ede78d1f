<?php
/**
 * Abstract Public Controller
 * 
 * Base controller for public pages that don't require authentication
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 */

abstract class AccountMgmt_Controller_AbstractPublicController extends AccountMgmt_Controller_AbstractCommonController
{
    /**
     * @override
     */
    final public function init()
    {
        // Call child controller initialization
        $this->_init();
        
        // Initialize common UI state for public pages
        $this->_initCommonUiState();
        
        // Initialize public-specific UI state
        $this->_initPublicUiState();
    }

    /**
     * Initialize UI state specific to public pages
     */
    protected function _initPublicUiState()
    {
        // Public pages might have different UI requirements
        // Override in child controllers if needed
    }

    /**
     * Get session - public pages may or may not have sessions
     * @return Zend_Session_Namespace|null
     */
    protected function getSession()
    {
        try {
            return AccountMgmt_Service_User::getSession();
        } catch (Exception $e) {
            // Public pages should handle missing sessions gracefully
            return null;
        }
    }
}
