<?php
/**
 * Abstract Common Controller
 * 
 * Base controller containing shared functionality for both public and restricted controllers
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 */

abstract class AccountMgmt_Controller_AbstractCommonController extends Zend_Controller_Action
{
    // Tab constants - shared across all controllers
    const TAB_HOME          = 'home';
    const TAB_BILLING       = 'billing';
    const TAB_FACILITY      = 'facilities';
    const TAB_SETTINGS      = 'settings';
    const TAB_WIDGET        = 'widget';
    const TAB_HOSTEDWEBSITE = 'hostedwebsite';
    const TAB_MOVE_INS      = 'move-ins';

    /**
     * Initialize controller - override in child controllers
     */
    protected function _init()
    {
        // Override in child controllers
    }

    /**
     * Initialize common UI state
     */
    protected function _initCommonUiState()
    {
        $this->view->selectedTab = $this->getTab();

        if ($this->getParam('welcome')) {
            $this->view->welcomeMessage = true;
        }

        // Initialize common view properties
        $this->view->errorMessages = array();
        $this->view->successMessages = array();
    }

    /**
     * Get the current tab for navigation - override in child controllers
     */
    protected function getTab()
    {
        return '';
    }

    /**
     * Get sidebar content - override in child controllers
     */
    protected function getSideBarContent()
    {
        return '';
    }

    /**
     * Dispatch error messages
     */
    protected function dispatchError($messages)
    {
        $view = new Zend_View();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/error/');
        $view->errorMessages = array($messages);
        echo $view->render('redbox.phtml');
    }

    /**
     * Dispatch success messages
     */
    protected function dispatchSuccess($messages)
    {
        $view = new Zend_View();
        $view->setScriptPath(APPLICATION_PATH . '/views/scripts/error/');
        $view->successMessages = array($messages);
        echo $view->render('greenbox.phtml');
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     *
     * @param string $paramName
     * @param mixed $default
     * @return mixed
     */
    public function getParam($paramName, $default = null)
    {
        $param = parent::getParam($paramName, $default);
        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }
}
